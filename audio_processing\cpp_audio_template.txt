// This file is auto-generated by preprocess_audio_files.py. Do not edit this manually.
#pragma once

/*
{meta_data}
*/

typedef struct {{
    int frequency;  // Frequency in Hz (0 = silence)
    int duration;   // Duration in milliseconds
}} ToneNote;

constexpr int {array_name}_count = {tone_count};
constexpr int {array_name}_total_duration = {total_duration}; // milliseconds
constexpr ToneNote {array_name}_sequence[] = {{
{tone_data}
}};
