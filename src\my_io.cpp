#include "my_io.h"
#include "audio/audio_sequences.h"

#include <Arduino.h>
#include <esp_sleep.h>
#include <driver/rtc_io.h>
#include <driver/ledc.h>
#include <WiFi.h>
#include <esp_bt.h>

// Define the enum value if not available
#ifndef ESP_EXT1_WAKEUP_ANY_LOW
#define ESP_EXT1_WAKEUP_ANY_LOW ((esp_sleep_ext1_wakeup_mode_t) 1)
#endif

//IO pins
#define ENC_BUTTON 17
#define ENC_DATA 21
#define ENC_CLK 13

#define LED_DATA 0  // Two WS2812-2020 chips

#define BUZZER 12
#define VCC_IN 34  // Battery voltage mesure pin

RTC_DATA_ATTR int bootCount = 0;

// Encoder state variables
static int lastEncoderCLK = HIGH;
static int lastEncoderDATA = HIGH;

/*
Method to print the reason by which <PERSON><PERSON><PERSON>
has been awaken from sleep
*/
void print_wakeup_reason() {
  esp_sleep_wakeup_cause_t wakeup_reason;

  wakeup_reason = esp_sleep_get_wakeup_cause();

  switch (wakeup_reason) {
    case ESP_SLEEP_WAKEUP_EXT0: {
      Serial.println("Wakeup caused by external signal using RTC_IO");
      // Check which pin caused the wake-up
      // Note: esp_sleep_get_ext0_wakeup_pin() may not be available in all ESP32 versions
      Serial.println("  -> Encoder button or rotation detected!");
      tone(1500, 50); // Short tone to confirm wake-up
      break;
    }
    case ESP_SLEEP_WAKEUP_EXT1: {
      Serial.println("Wakeup caused by external signal using RTC_CNTL");
      // Check which pin(s) caused the wake-up
      uint64_t wakeup_pin_mask = esp_sleep_get_ext1_wakeup_status();
      if (wakeup_pin_mask & (1ULL << GPIO_NUM_17)) {
        Serial.println("  -> Encoder button pressed!");
        tone(1500, 50);
      }
      if (wakeup_pin_mask & (1ULL << GPIO_NUM_13)) {
        Serial.println("  -> Encoder rotated!");
        tone(1000, 30);
      }
      break;
    }
    case ESP_SLEEP_WAKEUP_TIMER: {
      Serial.println("Wakeup caused by timer");
      break;
    }
    case ESP_SLEEP_WAKEUP_TOUCHPAD: {
      Serial.println("Wakeup caused by touchpad");
      break;
    }
    case ESP_SLEEP_WAKEUP_ULP: {
      Serial.println("Wakeup caused by ULP program");
      break;
    }
    default: {
      Serial.printf("Wakeup was not caused by deep sleep: %d\n", wakeup_reason);
      tone(2000, 100); // Different tone for first boot
      break;
    }
  }
}

void IOSetup() {
  //initialize Serial Monitor
  Serial.begin(9600);
  while (!Serial){
    delay(10);  // Wait for Serial to be ready
  }

  ++bootCount;
  Serial.println("Boot number: " + String(bootCount));

  print_wakeup_reason();

  pinMode(ENC_BUTTON, INPUT_PULLUP); // Enable internal pull-up for button
  pinMode(ENC_DATA, INPUT_PULLUP);   // Enable internal pull-up for encoder
  pinMode(ENC_CLK, INPUT_PULLUP);    // Enable internal pull-up for encoder

  // Initialize encoder state
  lastEncoderCLK = digitalRead(ENC_CLK);
  lastEncoderDATA = digitalRead(ENC_DATA);

  pinMode(LED_DATA, OUTPUT);

  pinMode(BUZZER, OUTPUT);
  pinMode(VCC_IN, INPUT);

  WiFi.mode(WIFI_OFF);
  btStop();

  //testBeep();
}

void IOEnd() {
  float voltage = batteryVoltage();  // Get the current battery voltage
  if (batteryEmpty(voltage)) {
    Serial.println("Battery empty");
    error();
  }
}

//reads battery voltage
float batteryVoltage() {
  int voltReading = analogRead(VCC_IN);
  const float MaxVoltage = 3.3;  //V
  const int Reslolution = 4095;
  const int R1 = 27;   //kΩ
  const int R2 = 100;  //kΩ
  const float cRate = MaxVoltage * (R1 + R2) / R2 / Reslolution;
  return voltReading * cRate;
}

void error() {
  tone(2000, 100);
  Serial.flush();
  esp_sleep_disable_wakeup_source(ESP_SLEEP_WAKEUP_ALL);
  esp_deep_sleep_start();
}

void tone(int frequency, int duration) {
  // Check if frequency and duration are valid
  if (duration <= 0) {
    return;
  }

  const int buzzerChannel = 0;  // Use LEDC channel 0
  const int resolution = 8;     // 8-bit resolution

  if (frequency <= 0) {
    // Silence - just delay without generating tone
    delay(duration);
    return;
  }

  // Setup LEDC channel
  ledcSetup(buzzerChannel, frequency, resolution);
  ledcAttachPin(BUZZER, buzzerChannel);

  // Start the tone
  ledcWrite(buzzerChannel, 128); // 50% duty cycle (128 out of 255)

  delay(duration);  // Wait for the tone to complete

  // Stop the tone
  ledcWrite(buzzerChannel, 0);
  ledcDetachPin(BUZZER);
}

void playToneSequence(const ToneNote* sequence, int count) {
  if (!sequence || count <= 0) {
    return;
  }

  for (int i = 0; i < count; i++) {
    tone(sequence[i].frequency, sequence[i].duration);
    // Small gap between notes for clarity
    if (i < count - 1) {
      delay(10);
    }
  }
}

// testBeep function removed - use tone() or playToneSequence() instead

// Configure encoder rotation as wake-up source
void setupEncoderRotationWakeup() {
  // Use CLK pin (pin 13) to detect rotation
  // Note: Pin 13 is RTC_GPIO capable
  esp_sleep_enable_ext0_wakeup(GPIO_NUM_13, 0); // Wake on falling edge
  Serial.println("Encoder rotation wake-up enabled (pin 13)");
}

// Check if encoder button is currently pressed
bool isEncoderButtonPressed() {
  return digitalRead(ENC_BUTTON) == LOW; // Button is active LOW
}

// Read encoder rotation direction
// Returns: -1 for counter-clockwise, 0 for no change, 1 for clockwise
int readEncoderRotation() {
  int currentCLK = digitalRead(ENC_CLK);
  int currentDATA = digitalRead(ENC_DATA);

  // Check if CLK pin has changed (rotation detected)
  if (currentCLK != lastEncoderCLK) {
    // Update last state
    lastEncoderCLK = currentCLK;

    // If CLK went from HIGH to LOW (falling edge)
    if (currentCLK == LOW) {
      // Check DATA pin to determine direction
      if (currentDATA == HIGH) {
        return 1;  // Clockwise rotation
      } else {
        return -1; // Counter-clockwise rotation
      }
    }
  }

  return 0; // No rotation detected
}

// Get current boot count
int getBootCount() {
  return bootCount;
}

// Handle encoder input when awake
void handleEncoderInput() {
  static unsigned long lastEncoderCheck = 0;
  unsigned long currentTime = millis();

  // Check encoder every 50ms to avoid bouncing
  if (currentTime - lastEncoderCheck > 50) {
    lastEncoderCheck = currentTime;

    // Check for button press
    if (isEncoderButtonPressed()) {
      Serial.println("Encoder button is pressed!");
      tone(1500, 100); // Confirmation tone
      delay(200); // Debounce delay
    }

    // Check for rotation
    int rotation = readEncoderRotation();
    if (rotation != 0) {
      if (rotation > 0) {
        Serial.println("Encoder rotated clockwise");
        tone(1200, 50);
      } else {
        Serial.println("Encoder rotated counter-clockwise");
        tone(800, 50);
      }
    }
  }
}

// AudioPlayer implementation
#ifdef __cplusplus
void AudioPlayer::play() const {
  playToneSequence(audio_data->sequence, audio_data->count);
}

void AudioPlayer::play(int start_note, int end_note) const {
  if (!audio_data->sequence || start_note < 0 || end_note >= audio_data->count || start_note > end_note) {
    return;
  }

  int count = end_note - start_note + 1;
  playToneSequence(&audio_data->sequence[start_note], count);
}
#endif