"""
Audio processing module for dosimeter project.

This module contains functionality for:
- MP3 audio file processing and analysis
- Frequency extraction and tone sequence generation
- C++ header generation for buzzer playback
- Test audio generation
"""

from .preprocess_audio_files import preprocess_audio_files
from .create_test_audio import create_test_audio

__all__ = [
    'preprocess_audio_files',
    'create_test_audio'
]
