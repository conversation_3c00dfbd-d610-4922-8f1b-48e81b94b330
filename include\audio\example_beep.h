// This file is an example of auto-generated audio header. Do not edit this manually.
#pragma once

/*
Example beep sequence - 1000Hz for 500ms
*/

typedef struct {
    int frequency;  // Frequency in Hz (0 = silence)
    int duration;   // Duration in milliseconds
} ToneNote;

constexpr int example_beep_count = 1;
constexpr int example_beep_total_duration = 500; // milliseconds
constexpr ToneNote example_beep_sequence[] = {
    {1000, 500}
};
