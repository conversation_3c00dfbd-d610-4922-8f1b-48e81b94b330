#!/usr/bin/env python3
"""
Generate test audio files for testing the audio processing pipeline.
This script creates simple tone sequences and saves them as MP3 files.
"""

import os
import argparse
import numpy as np
from pathlib import Path
from typing import List, Tuple
import soundfile as sf
from scipy.io import wavfile


def create_tone_sequence(frequencies: List[int], durations: List[float], sample_rate: int = 22050) -> np.ndarray:
    """
    Create a tone sequence from frequencies and durations.
    
    Args:
        frequencies: List of frequencies in Hz (0 for silence)
        durations: List of durations in seconds
        sample_rate: Audio sample rate
        
    Returns:
        Audio data as numpy array
    """
    if len(frequencies) != len(durations):
        raise ValueError("Frequencies and durations must have the same length")
    
    audio_data = []
    
    for freq, duration in zip(frequencies, durations):
        samples = int(duration * sample_rate)
        t = np.linspace(0, duration, samples, False)
        
        if freq == 0:
            # Silence
            tone = np.zeros(samples)
        else:
            # Generate sine wave
            tone = 0.3 * np.sin(2 * np.pi * freq * t)
        
        audio_data.extend(tone)
    
    return np.array(audio_data, dtype=np.float32)


def create_test_beep_audio(output_dir: Path = None) -> str:
    """Create a test beep audio similar to the original beep function."""
    if output_dir is None:
        output_dir = Path("audio-export")
    
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Create a simple beep sequence: 1000Hz for 500ms
    frequencies = [1000]
    durations = [0.5]
    
    audio_data = create_tone_sequence(frequencies, durations)
    
    # Save as WAV first, then we'll need to convert to MP3
    output_path = output_dir / "test_beep.wav"
    wavfile.write(output_path, 22050, audio_data)
    
    print(f"Created test beep audio: {output_path}")
    return str(output_path)


def create_test_melody_audio(output_dir: Path = None) -> str:
    """Create a test melody audio."""
    if output_dir is None:
        output_dir = Path("audio-export")
    
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Create a simple melody: C-D-E-F-G notes
    frequencies = [262, 294, 330, 349, 392]  # C4, D4, E4, F4, G4
    durations = [0.3, 0.3, 0.3, 0.3, 0.5]
    
    audio_data = create_tone_sequence(frequencies, durations)
    
    # Save as WAV
    output_path = output_dir / "test_melody.wav"
    wavfile.write(output_path, 22050, audio_data)
    
    print(f"Created test melody audio: {output_path}")
    return str(output_path)


def create_test_alert_audio(output_dir: Path = None) -> str:
    """Create a test alert audio with alternating tones."""
    if output_dir is None:
        output_dir = Path("audio-export")
    
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Create alternating high-low alert tones
    frequencies = [1500, 800, 1500, 800, 1500]
    durations = [0.2, 0.2, 0.2, 0.2, 0.3]
    
    audio_data = create_tone_sequence(frequencies, durations)
    
    # Save as WAV
    output_path = output_dir / "test_alert.wav"
    wavfile.write(output_path, 22050, audio_data)
    
    print(f"Created test alert audio: {output_path}")
    return str(output_path)


def create_test_audio(output_dir: Path = None) -> List[str]:
    """Create all test audio files."""
    created_files = []
    
    created_files.append(create_test_beep_audio(output_dir))
    created_files.append(create_test_melody_audio(output_dir))
    created_files.append(create_test_alert_audio(output_dir))
    
    return created_files


def main():
    """Main function for command line usage."""
    parser = argparse.ArgumentParser(description="Generate test audio files for buzzer conversion")
    parser.add_argument("--output-dir", type=Path, default=Path("audio-export"),
                       help="Output directory for test audio files")
    
    args = parser.parse_args()
    
    print("Generating test audio files...")
    created_files = create_test_audio(args.output_dir)
    
    print(f"\nGenerated {len(created_files)} test audio files:")
    for file_path in created_files:
        print(f"  - {file_path}")
    
    print("\nNote: These are WAV files. You may need to convert them to MP3 for the audio processing pipeline.")
    
    return 0


if __name__ == "__main__":
    exit(main())
