"""
Core audio processing functions for converting MP3 files to buzzer tone sequences.
"""

import os
import numpy as np
from pathlib import Path
from typing import List, Tuple, Optional
import librosa
import librosa.display


def get_script_dir() -> Path:
    """Get the directory of this script, fallback to cwd if __file__ is not defined."""
    try:
        return Path(__file__).parent
    except NameError:
        return Path(os.getcwd())


def get_template_path(template_name: str) -> Path:
    """Get the path to a template file, checking multiple possible locations."""
    script_dir = get_script_dir()
    
    # Try current directory first
    if (script_dir / template_name).exists():
        return script_dir / template_name
    
    # Try parent directory (for when running from project root)
    if (script_dir.parent / template_name).exists():
        return script_dir.parent / template_name
    
    # Try audio_processing directory specifically
    if (script_dir.parent / "audio_processing" / template_name).exists():
        return script_dir.parent / "audio_processing" / template_name

    # Fallback: original path (will cause error if not found)
    return script_dir / template_name


def load_cpp_audio_template() -> str:
    """Load the C++ audio template."""
    template_path = get_template_path("cpp_audio_template.txt")
    with open(template_path, "r") as f:
        return f.read()


def load_audio_master_template() -> str:
    """Load the C++ master audio header template."""
    template_path = get_template_path("cpp_audio_master_template.txt")
    with open(template_path, "r") as f:
        return f.read()


def extract_dominant_frequencies(audio_file: Path, segment_duration: float = 0.1, 
                                max_segments: int = 100) -> List[Tuple[int, int]]:
    """
    Extract dominant frequencies from an audio file and convert to tone sequences.
    
    Args:
        audio_file: Path to the MP3 file
        segment_duration: Duration of each segment in seconds (default 0.1s = 100ms)
        max_segments: Maximum number of segments to process
        
    Returns:
        List of (frequency, duration_ms) tuples suitable for buzzer playback
    """
    try:
        # Load audio file
        y, sr = librosa.load(audio_file, sr=None)
        
        # Calculate segment size in samples
        segment_samples = int(segment_duration * sr)
        duration_ms = int(segment_duration * 1000)
        
        # Limit total segments
        total_samples = min(len(y), max_segments * segment_samples)
        y = y[:total_samples]
        
        tone_sequence = []
        
        # Process audio in segments
        for i in range(0, len(y), segment_samples):
            segment = y[i:i + segment_samples]
            
            if len(segment) < segment_samples // 2:  # Skip very short segments
                break
                
            # Compute FFT to find dominant frequency
            fft = np.fft.fft(segment)
            freqs = np.fft.fftfreq(len(segment), 1/sr)
            
            # Only consider positive frequencies
            positive_freqs = freqs[:len(freqs)//2]
            positive_fft = np.abs(fft[:len(fft)//2])
            
            # Find dominant frequency (excluding DC component)
            if len(positive_fft) > 1:
                dominant_idx = np.argmax(positive_fft[1:]) + 1
                dominant_freq = positive_freqs[dominant_idx]
                
                # Convert to integer frequency, constrain to buzzer range (100-5000 Hz)
                freq_int = int(abs(dominant_freq))
                freq_int = max(100, min(5000, freq_int))
                
                # Only add if frequency is reasonable and has sufficient amplitude
                amplitude = positive_fft[dominant_idx]
                if amplitude > np.max(positive_fft) * 0.1:  # At least 10% of max amplitude
                    tone_sequence.append((freq_int, duration_ms))
                else:
                    # Add silence for low amplitude segments
                    tone_sequence.append((0, duration_ms))
            else:
                tone_sequence.append((0, duration_ms))
        
        return tone_sequence
        
    except Exception as e:
        print(f"Error processing audio file {audio_file}: {e}")
        return []


def optimize_tone_sequence(tone_sequence: List[Tuple[int, int]], 
                          min_duration: int = 50) -> List[Tuple[int, int]]:
    """
    Optimize tone sequence by merging similar consecutive frequencies and removing very short tones.
    
    Args:
        tone_sequence: List of (frequency, duration) tuples
        min_duration: Minimum duration for a tone in milliseconds
        
    Returns:
        Optimized tone sequence
    """
    if not tone_sequence:
        return []
    
    optimized = []
    current_freq, current_duration = tone_sequence[0]
    
    for freq, duration in tone_sequence[1:]:
        # If frequency is similar (within 5% or same), merge durations
        if (freq == current_freq or 
            (current_freq > 0 and freq > 0 and abs(freq - current_freq) / current_freq < 0.05)):
            current_duration += duration
        else:
            # Add current tone if it meets minimum duration
            if current_duration >= min_duration:
                optimized.append((current_freq, current_duration))
            current_freq, current_duration = freq, duration
    
    # Add the last tone
    if current_duration >= min_duration:
        optimized.append((current_freq, current_duration))
    
    return optimized


def tone_sequence_to_c_array(tone_sequence: List[Tuple[int, int]]) -> str:
    """Convert tone sequence to C array format."""
    if not tone_sequence:
        return "    // Empty tone sequence"
    
    lines = []
    for i, (freq, duration) in enumerate(tone_sequence):
        comma = "," if i < len(tone_sequence) - 1 else ""
        lines.append(f"    {{{freq}, {duration}}}{comma}")
    
    return "\n".join(lines)


def clean_audio_destination_folder(dst_folder: Path, src_folder: Path):
    """Clean destination folder of old audio header files."""
    if not dst_folder.exists():
        return
    
    # Get list of source MP3 files (without extension)
    src_files = {f.stem for f in src_folder.glob("*.mp3")}
    
    # Remove header files that don't have corresponding MP3 files
    for header_file in dst_folder.glob("*.h"):
        if header_file.stem not in src_files and header_file.name != "audio_sequences.h":
            print(f"Removing orphaned header: {header_file}")
            header_file.unlink()


def process_audio_file(input_path: Path, output_path: Path, 
                      segment_duration: float = 0.1, max_segments: int = 100) -> str:
    """
    Process audio file and generate C++ header.
    
    Args:
        input_path: Path to input MP3 file
        output_path: Path for output header file (without extension)
        segment_duration: Duration of each audio segment to analyze
        max_segments: Maximum number of segments to process
        
    Returns:
        Audio type description
    """
    print(f"Processing {input_path.name}...")
    
    # Extract tone sequence from MP3
    tone_sequence = extract_dominant_frequencies(input_path, segment_duration, max_segments)
    
    if not tone_sequence:
        print(f"\t{input_path.name}: No audio data extracted!")
        return "empty"
    
    # Optimize the sequence
    optimized_sequence = optimize_tone_sequence(tone_sequence)
    
    if not optimized_sequence:
        print(f"\t{input_path.name}: No valid tones after optimization!")
        return "empty"
    
    print(f"\t{input_path.name}: Extracted {len(optimized_sequence)} tone segments")
    
    # Generate C++ header
    array_name = output_path.stem.replace("-", "_")
    cpp_template = load_cpp_audio_template()
    
    tone_array_str = tone_sequence_to_c_array(optimized_sequence)
    total_duration = sum(duration for _, duration in optimized_sequence)
    
    header_content = cpp_template.format(
        array_name=array_name,
        tone_count=len(optimized_sequence),
        total_duration=total_duration,
        tone_data=tone_array_str,
        meta_data=f"Generated from {input_path.name} - {len(optimized_sequence)} tones, {total_duration}ms total"
    )
    
    with open(output_path.with_suffix(".h"), "w") as f:
        f.write(header_content)
    
    return f"{len(optimized_sequence)} tones"
