import os
from pathlib import Path
from typing import Optional, Any
from .core import (
    process_audio_file,
    clean_audio_destination_folder,
    load_audio_master_template
)


def preprocess_audio_files(source: Optional[Any] = None, target: Optional[Any] = None, env: Optional[Any] = None) -> None:
    """Process MP3 files from audio-export to C++ headers."""
    src_folder = Path("audio-export")
    dst_folder = Path("include/audio")

    src_folder.mkdir(parents=True, exist_ok=True)
    dst_folder.mkdir(parents=True, exist_ok=True)

    clean_audio_destination_folder(dst_folder, src_folder)
    print(f"Processing MP3 files from {src_folder} to {dst_folder}")
    print("Converting MP3 audio to buzzer tone sequences")

    # Collect audio info for master header
    audio_entries = []
    enum_entries = []
    include_entries = []

    for file_path in src_folder.glob("*.mp3"):
        file_name = file_path.stem

        # Skip test audio files - they should be processed separately
        if file_name.startswith("test_"):
            print(f"Skipping test audio: {file_path.name} (use create_test_audio script for test audio)")
            continue

        output_file = dst_folder / (file_name + ".h")
        print(f"Processing {file_path} -> {output_file}")

        # Process file and get audio type info
        audio_type = process_audio_file(file_path, output_file)
        print()

        if audio_type != "empty":
            array_name = file_name.replace("-", "_")
            audio_entries.append(f'    {{ {array_name}_sequence, {array_name}_count, {array_name}_total_duration }}')
            enum_entries.append(f'    AUDIO_{array_name.upper()}')
            include_entries.append(f'#include "{file_name}.h"')

    # Write master header using template
    master_header = dst_folder / "audio_sequences.h"
    master_template = load_audio_master_template()

    # Debug output
    print(f"DEBUG: Creating master audio header with {len(include_entries)} includes:")
    for inc in include_entries:
        print(f"  {inc}")

    header_content = master_template.format(
        includes="\n".join(include_entries),
        enum_entries=",\n".join(enum_entries),
        audio_entries=",\n".join(audio_entries)
    )

    # Only overwrite if content changed
    if not master_header.exists() or master_header.read_text(encoding="utf-8") != header_content:
        with open(master_header, "w", encoding="utf-8") as f:
            f.write(header_content)
        print(f"Master audio header generated: {master_header}")
    else:
        print(f"Master audio header unchanged: {master_header}")
    print("All MP3 files have been processed and saved as C++ headers.")


# Direct execution support
if __name__ == "__main__":
    preprocess_audio_files()
