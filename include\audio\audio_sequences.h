// This file is auto-generated by preprocess_audio_files.py. Do not edit this manually.
#pragma once

#include "example_beep.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    int frequency;  // Frequency in Hz (0 = silence)
    int duration;   // Duration in milliseconds
} ToneNote;

typedef struct {
    const ToneNote* sequence;
    int count;
    int total_duration;
} AudioSequence;

typedef enum {
    AUDIO_EXAMPLE_BEEP
} AudioId;

static const AudioSequence audioTable[] = {
    { example_beep_sequence, example_beep_count, example_beep_total_duration }
};

static inline const AudioSequence* getAudioSequence(AudioId id) { return &audioTable[id]; }

#ifdef __cplusplus
}

// C++ Audio Classes
#ifdef __cplusplus

class AudioPlayer {
private:
    const AudioSequence* audio_data;
    
public:
    AudioPlayer(AudioId id) : audio_data(getAudioSequence(id)) {}
    
    // Play the entire audio sequence
    void play() const;
    
    // Play a portion of the sequence
    void play(int start_note, int end_note) const;
    
    // Get sequence properties
    int count() const { return audio_data->count; }
    int total_duration() const { return audio_data->total_duration; }
    const ToneNote* sequence() const { return audio_data->sequence; }
    
    // Get individual note
    ToneNote note(int index) const {
        if (index >= 0 && index < audio_data->count) {
            return audio_data->sequence[index];
        }
        return {0, 0}; // Return silence for invalid index
    }
};

#endif
#endif
